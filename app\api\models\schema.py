from pydantic import BaseModel, <PERSON><PERSON>
from typing import Any
from llama_index.core import Settings

class SourceDocument(BaseModel):
    Id: str
    Text: str | None = None
    IndexType: str | None = None
    Metadata: Json[Any]
    ExcludedLLMMetadataKeys: list[str]

class MetadataFilterRequest(BaseModel):
    Key: str
    Operator: str
    Value: str

class MetadataFiltersRequest(BaseModel):
    Filters: list[MetadataFilterRequest]
    Condition: str = "and"

class QueryParams(BaseModel):
    Query: str
    History: str | None = None
    Filters: MetadataFiltersRequest | None = None

class ChatParams(BaseModel):
    Query: str
    History: str | None = None
    Filters: dict | None = {}

class TokenUsageModel(BaseModel):
    TotalLLMToken: int
    TotalEmbeddingToken: int
    PromptLLMToken: int
    CompletionLLMToken: int
    Model: str = ""

    def __init__(self, llm_token, embedding_token, prompt_llm_token_count, completion_llm_token_count, model):
        super().__init__(TotalLLMToken = llm_token, TotalEmbeddingToken = embedding_token, PromptLLMToken=prompt_llm_token_count, CompletionLLMToken=completion_llm_token_count, Model=model)

class ChatResponse(BaseModel):
    TokenUsage: TokenUsageModel | None
    ChatStore: str
    
    def __init__(self, chat_store, token_usage):
        super().__init__(ChatStore = chat_store, TokenUsage = token_usage)

class RetrieveItem(BaseModel):
    Id: str
    IndexType: str

    def __eq__(self, other) : 
        return self.Id == other.Id

class SearchResponse(BaseModel):
    Items: list[RetrieveItem] = []
    TokenUsage: TokenUsageModel | None

    def __init__(self, items, token_usage):
        super().__init__(Items = items, TokenUsage = token_usage)

class QueryResponse(BaseModel):
    Response: str | None
    TokenUsage: TokenUsageModel

    def __init__(self, answer, token_usage):
        super().__init__(Response = answer, TokenUsage = token_usage)
