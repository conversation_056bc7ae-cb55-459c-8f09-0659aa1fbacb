# index_with_ids.py
import logging
from typing import Union, Iterable, Sequence, Callable, Optional, Dict, Literal, cast
from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStore
from langchain_core.indexing.api import _HashedDocument, _deduplicate_in_order, _batch, _get_source_id_assigner, _delete, IndexingResult
from langchain_core.indexing.base import DocumentIndex, RecordManager
from langchain_core.document_loaders.base import BaseLoader
import re
from typing import Tuple

from llama_index.core.agent.react.types import (
    ActionReasoningStep,
    BaseReasoningStep,
    ResponseReasoningStep,
)
from llama_index.core.types import BaseOutputParser

from llama_index.core.agent.react.output_parser import (parse_action_reasoning_step, extract_final_response)
from pydantic import BaseModel, Field
import mimetypes
from pathlib import Path
from typing import Optional, BinaryIO

class IndexingWithDocResult(IndexingResult):
    """Return a detailed a breakdown of the result of the indexing operation."""
    docs_to_index: list[any] = []

def index_with_ids(
    docs_source: Union[BaseLoader, Iterable[Document]],
    record_manager: RecordManager,
    vector_store: Union[VectorStore, DocumentIndex],
    *,
    batch_size: int = 100,
    cleanup: Literal["incremental", "full", "scoped_full", None] = None,
    source_id_key: Union[str, Callable[[Document], str], None] = None,
    cleanup_batch_size: int = 1_000,
    force_update: bool = False,
    upsert_kwargs: Optional[dict[str, any]] = None,
) -> IndexingWithDocResult:
    """Index data from the loader into the vector store.

    Indexing functionality uses a manager to keep track of which documents
    are in the vector store.

    This allows us to keep track of which documents were updated, and which
    documents were deleted, which documents should be skipped.

    For the time being, documents are indexed using their hashes, and users
     are not able to specify the uid of the document.

    Important:
       * In full mode, the loader should be returning
         the entire dataset, and not just a subset of the dataset.
         Otherwise, the auto_cleanup will remove documents that it is not
         supposed to.
       * In incremental mode, if documents associated with a particular
         source id appear across different batches, the indexing API
         will do some redundant work. This will still result in the
         correct end state of the index, but will unfortunately not be
         100% efficient. For example, if a given document is split into 15
         chunks, and we index them using a batch size of 5, we'll have 3 batches
         all with the same source id. In general, to avoid doing too much
         redundant work select as big a batch size as possible.
        * The `scoped_full` mode is suitable if determining an appropriate batch size
          is challenging or if your data loader cannot return the entire dataset at
          once. This mode keeps track of source IDs in memory, which should be fine
          for most use cases. If your dataset is large (10M+ docs), you will likely
          need to parallelize the indexing process regardless.

    Args:
        docs_source: Data loader or iterable of documents to index.
        record_manager: Timestamped set to keep track of which documents were
                         updated.
        vector_store: VectorStore or DocumentIndex to index the documents into.
        batch_size: Batch size to use when indexing. Default is 100.
        cleanup: How to handle clean up of documents. Default is None.
            - incremental: Cleans up all documents that haven't been updated AND
                           that are associated with source ids that were seen
                           during indexing.
                           Clean up is done continuously during indexing helping
                           to minimize the probability of users seeing duplicated
                           content.
            - full: Delete all documents that have not been returned by the loader
                    during this run of indexing.
                    Clean up runs after all documents have been indexed.
                    This means that users may see duplicated content during indexing.
            - scoped_full: Similar to Full, but only deletes all documents
                           that haven't been updated AND that are associated with
                           source ids that were seen during indexing.
            - None: Do not delete any documents.
        source_id_key: Optional key that helps identify the original source
            of the document. Default is None.
        cleanup_batch_size: Batch size to use when cleaning up documents.
            Default is 1_000.
        force_update: Force update documents even if they are present in the
            record manager. Useful if you are re-indexing with updated embeddings.
            Default is False.
        upsert_kwargs: Additional keyword arguments to pass to the add_documents
                       method of the VectorStore or the upsert method of the
                       DocumentIndex. For example, you can use this to
                       specify a custom vector_field:
                       upsert_kwargs={"vector_field": "embedding"}
            .. versionadded:: 0.3.10

    Returns:
        Indexing result which contains information about how many documents
        were added, updated, deleted, or skipped.

    Raises:
        ValueError: If cleanup mode is not one of 'incremental', 'full' or None
        ValueError: If cleanup mode is incremental and source_id_key is None.
        ValueError: If vectorstore does not have
            "delete" and "add_documents" required methods.
        ValueError: If source_id_key is not None, but is not a string or callable.

    .. version_modified:: 0.3.25

        * Added `scoped_full` cleanup mode.
    """
    if cleanup not in {"incremental", "full", "scoped_full", None}:
        msg = (
            f"cleanup should be one of 'incremental', 'full', 'scoped_full' or None. "
            f"Got {cleanup}."
        )
        raise ValueError(msg)

    if (cleanup == "incremental" or cleanup == "scoped_full") and source_id_key is None:
        msg = (
            "Source id key is required when cleanup mode is incremental or scoped_full."
        )
        raise ValueError(msg)

    destination = vector_store  # Renaming internally for clarity

    # If it's a vectorstore, let's check if it has the required methods.
    if isinstance(destination, VectorStore):
        # Check that the Vectorstore has required methods implemented
        methods = ["delete", "add_documents"]

        for method in methods:
            if not hasattr(destination, method):
                msg = (
                    f"Vectorstore {destination} does not have required method {method}"
                )
                raise ValueError(msg)

        if type(destination).delete == VectorStore.delete:
            # Checking if the vectorstore has overridden the default delete method
            # implementation which just raises a NotImplementedError
            msg = "Vectorstore has not implemented the delete method"
            raise ValueError(msg)
    elif isinstance(destination, DocumentIndex):
        pass
    else:
        msg = (
            f"Vectorstore should be either a VectorStore or a DocumentIndex. "
            f"Got {type(destination)}."
        )
        raise TypeError(msg)

    if isinstance(docs_source, BaseLoader):
        try:
            doc_iterator = docs_source.lazy_load()
        except NotImplementedError:
            doc_iterator = iter(docs_source.load())
    else:
        doc_iterator = iter(docs_source)

    source_id_assigner = _get_source_id_assigner(source_id_key)

    # Mark when the update started.
    index_start_dt = record_manager.get_time()
    
    # Initialize variables at start
    num_added = 0
    num_skipped = 0 
    num_updated = 0
    num_deleted = 0
    docs_to_index = []  # Initialize docs_to_index list

    scoped_full_cleanup_source_ids: set[str] = set()

    for doc_batch in _batch(batch_size, doc_iterator):
        hashed_docs = list(
            _deduplicate_in_order(
                [_HashedDocument.from_document(doc) for doc in doc_batch]
            )
        )

        source_ids: Sequence[Optional[str]] = [
            source_id_assigner(doc) for doc in hashed_docs
        ]

        if cleanup == "incremental" or cleanup == "scoped_full":
            # source ids are required.
            for source_id, hashed_doc in zip(source_ids, hashed_docs):
                if source_id is None:
                    msg = (
                        f"Source ids are required when cleanup mode is "
                        f"incremental or scoped_full. "
                        f"Document that starts with "
                        f"content: {hashed_doc.page_content[:100]} was not assigned "
                        f"as source id."
                    )
                    raise ValueError(msg)
                if cleanup == "scoped_full":
                    scoped_full_cleanup_source_ids.add(source_id)
            # source ids cannot be None after for loop above.
            source_ids = cast(Sequence[str], source_ids)  # type: ignore[assignment]

        exists_batch = record_manager.exists([doc.uid for doc in hashed_docs])

        # Filter out documents that already exist in the record store.
        uids = []
        docs_to_index = []
        uids_to_refresh = []
        seen_docs: set[str] = set()
        for hashed_doc, doc_exists in zip(hashed_docs, exists_batch):
            if doc_exists:
                if force_update:
                    seen_docs.add(hashed_doc.uid)
                else:
                    uids_to_refresh.append(hashed_doc.uid)
                    continue
            uids.append(hashed_doc.uid)
            docs_to_index.append(hashed_doc.to_document())

        # Update refresh timestamp
        if uids_to_refresh:
            record_manager.update(uids_to_refresh, time_at_least=index_start_dt)
            num_skipped += len(uids_to_refresh)

        # Be pessimistic and assume that all vector store write will fail.
        # First write to vector store
        if docs_to_index:
            if isinstance(destination, VectorStore):
                destination.add_documents(
                    docs_to_index,
                    ids=uids,
                    batch_size=batch_size,
                    **(upsert_kwargs or {}),
                )
            elif isinstance(destination, DocumentIndex):
                destination.upsert(
                    docs_to_index,
                    **(upsert_kwargs or {}),
                )

            num_added += len(docs_to_index) - len(seen_docs)
            num_updated += len(seen_docs)

        # And only then update the record store.
        # Update ALL records, even if they already exist since we want to refresh
        # their timestamp.
        record_manager.update(
            [doc.uid for doc in hashed_docs],
            group_ids=source_ids,
            time_at_least=index_start_dt,
        )

        # If source IDs are provided, we can do the deletion incrementally!
        if cleanup == "incremental":
            # Get the uids of the documents that were not returned by the loader.

            # mypy isn't good enough to determine that source ids cannot be None
            # here due to a check that's happening above, so we check again.
            for source_id in source_ids:
                if source_id is None:
                    msg = (
                        "source_id cannot be None at this point. "
                        "Reached unreachable code."
                    )
                    raise AssertionError(msg)

            _source_ids = cast(Sequence[str], source_ids)

            uids_to_delete = record_manager.list_keys(
                group_ids=_source_ids, before=index_start_dt
            )
            if uids_to_delete:
                # Then delete from vector store.
                _delete(destination, uids_to_delete)
                # First delete from record store.
                record_manager.delete_keys(uids_to_delete)
                num_deleted += len(uids_to_delete)

    if cleanup == "full" or cleanup == "scoped_full":
        delete_group_ids: Optional[Sequence[str]] = None
        if cleanup == "scoped_full":
            delete_group_ids = list(scoped_full_cleanup_source_ids)
        while uids_to_delete := record_manager.list_keys(
            group_ids=delete_group_ids, before=index_start_dt, limit=cleanup_batch_size
        ):
            # First delete from record store.
            _delete(destination, uids_to_delete)
            # Then delete from record manager.
            record_manager.delete_keys(uids_to_delete)
            num_deleted += len(uids_to_delete)

    return IndexingWithDocResult(num_added=num_added, 
                                 num_updated=num_updated, 
                                 num_deleted=num_deleted,
                                 num_skipped=num_skipped,
                                 docs_to_index=docs_to_index)

class StopwordRemover():
    def __init__(self):
        self.stopwords = {
            "about", "after", "all", "also", "an", "and", "another", "any", "are", "as", "at", "be", "because", "been", "before", "being", "between",
            "both", "but", "by", "came", "can", "come", "could", "did", "do", "does", "each", "else", "for", "from", "get", "got", "had", "has", "have",
            "he", "her", "here", "him", "himself", "his", "how", "if", "in", "into", "is", "it", "its", "just", "like", "make", "many", "me", "might",
            "more", "most", "much", "must", "my", "never", "no", "now", "of", "on", "only", "or", "other", "our", "out", "over", "re", "said", "same",
            "see", "should", "since", "so", "some", "still", "such", "take", "than", "that", "the", "their", "them", "then", "there", "these", "they",
            "this", "those", "through", "to", "too", "under", "up", "use", "very", "want", "was", "way", "we", "well", "were", "what", "when", "where",
            "which", "while", "who", "will", "with", "would", "you", "your"
        }

    def remove_stopwords(self, text):
        return ' '.join([word for word in text.split() if word.lower() not in self.stopwords])
    

class MagentrixReActOutputParser(BaseOutputParser):
    """ReAct Output parser."""

    def parse(self, output: str, is_streaming: bool = False) -> BaseReasoningStep:
        """Parse output from ReAct agent.

        We expect the output to be in one of the following formats:
        1. If the agent need to use a tool to answer the question:
            ```
            Thought: <thought>
            Action: <action>
            Action Input: <action_input>
            ```
        2. If the agent can answer the question without any tools:
            ```
            Thought: <thought>
            Answer: <answer>
            ```
        """
        if "Thought:" not in output:
            # NOTE: handle the case where the agent directly outputs the answer
            # instead of following the thought-answer format
            # return parse_action_reasoning_step(output)
            if "Answer:" in output:            
                return ResponseReasoningStep(
                    thought=thought,
                    response=output,
                    is_streaming=is_streaming
                )
            else:
                output = "Thought: I need to use a tool to answer the question!\n" + output
                
                return parse_action_reasoning_step(output)

        # An "Action" should take priority over an "Answer"
        if "Action:" in output:
            return parse_action_reasoning_step(output)

        if "Answer:" in output:
            thought, answer = extract_final_response(output)
            return ResponseReasoningStep(
                thought=thought, response=answer, is_streaming=is_streaming
            )

        raise ValueError(f"Could not parse output: {output}")

    def format(self, output: str) -> str:
        """Format a query with structured output formatting instructions."""
        raise NotImplementedError


class DetailRetrieverInput(BaseModel):
    """Input to the retriever."""
    ids: list[str] = Field(description="list of ids to look up in retriever")
    query: str = Field(description="query to look up in retriever")

class ResponseOutput(BaseModel):
    cited_ids: list[str] = Field(description="list of source ids of answer")
    answer: str = Field(description="answer")

class FileDetector:
    def __init__(self):
        # More comprehensive text file extensions
        self.text_extensions = {
            '.txt', '.sql', '.cs', '.html', '.htm', '.xml', '.json', '.yaml', '.yml',
            '.py', '.js', '.ts', '.css', '.scss', '.sass', '.md', '.rst',
            '.java', '.cpp', '.c', '.h', '.hpp', '.php', '.rb', '.go',
            '.sh', '.bat', '.ps1', '.r', '.m', '.pl', '.swift', '.kt',
            '.vue', '.jsx', '.tsx', '.less', '.styl', '.cfg', '.ini',
            '.log', '.csv', '.tsv', '.properties', '.dockerfile'
        }
        
        self.text_mime_prefixes = {
            'text/', 'application/json', 'application/xml', 'application/javascript',
            'application/x-sql', 'application/x-sh', 'application/x-python-code'
        }

    def is_text_file(self, file_name: str, content_type: str, 
                                 file_content: Optional[bytes] = None) -> bool:
        """
        Comprehensive text file detection using multiple methods
        """
        # Method 1: Check MIME type
        if self._is_text_by_mime_type(content_type):
            return True
        
        # Method 2: Check file extension
        if self._is_text_by_extension(file_name):
            return True
        
        # Method 3: Detect MIME type from filename using mimetypes
        if self._is_text_by_filename_mime(file_name):
            return True
            
        return False

    def _is_text_by_mime_type(self, content_type: str) -> bool:
        """Check if MIME type indicates text content"""
        if not content_type:
            return False
            
        content_type_lower = content_type.lower().split(';')[0].strip()
        
        # Direct text types
        if content_type_lower.startswith('text/'):
            return True
            
        # Known text-based application types
        text_app_types = {
            'application/json', 'application/xml', 'application/javascript',
            'application/x-javascript', 'application/ecmascript',
            'application/x-sql', 'application/sql', 'application/x-sh',
            'application/x-python-code', 'application/x-httpd-php',
            'application/xhtml+xml', 'application/rss+xml',
            'application/atom+xml', 'application/x-yaml', 'snippet'
        }
        
        return content_type_lower in text_app_types

    def _is_text_by_extension(self, file_name: str) -> bool:
        """Check if file extension indicates text content"""
        if not file_name:
            return False
            
        # Get file extension (case-insensitive)
        ext = Path(file_name).suffix.lower()
        return ext in self.text_extensions

    def _is_text_by_filename_mime(self, file_name: str) -> bool:
        """Use mimetypes library to guess MIME type from filename"""
        if not file_name:
            return False
            
        mime_type, _ = mimetypes.guess_type(file_name)
        if mime_type:
            return self._is_text_by_mime_type(mime_type)
        return False

    def _is_mostly_printable(self, content: bytes, encoding: str, 
                           threshold: float = 0.85) -> bool:
        """Check if content is mostly printable characters"""
        try:
            text = content.decode(encoding)
            if not text:
                return False
                
            printable_count = sum(1 for char in text if char.isprintable() or char.isspace())
            ratio = printable_count / len(text)
            return ratio >= threshold
        except (UnicodeDecodeError, TypeError):
            return False

    def _binary_text_heuristics(self, sample: bytes) -> bool:
        """Apply heuristics to determine if binary data might be text"""
        if not sample:
            return False
        
        # Check for null bytes (strong indicator of binary)
        if b'\x00' in sample:
            return False
        
        # Check ratio of control characters
        control_chars = sum(1 for byte in sample if byte < 32 and byte not in {9, 10, 13})
        control_ratio = control_chars / len(sample)
        
        # If too many control characters, likely binary
        if control_ratio > 0.1:
            return False
        
        # Check for common text patterns
        text_indicators = [b'\n', b'\r\n', b' ', b'\t']
        has_text_patterns = any(indicator in sample for indicator in text_indicators)
        
        return has_text_patterns

