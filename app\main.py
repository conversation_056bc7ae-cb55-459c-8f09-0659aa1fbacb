import uvicorn
from app.core.config import app_settings
import os
from typing import Union
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from app.api.router import api_router
import logging

from app.web import app


logger = logging.getLogger(__name__)

if (__name__ == '__main__'):
    print("Running in AppEnvironment")

    uvicorn.run(
        "__main__:app",
        host=app_settings.HOST_ADDRESS,
        port=app_settings.PORT,
        workers=app_settings.UVICORN_WORKER_COUNT
    )