from llama_index.core.storage.chat_store import SimpleChatStore
from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.core import Settings
from llama_index.core.base.llms.types import MessageRole
from app.api.models.schema import QueryResponse, TokenUsageModel
import tiktoken
from llama_index.core.utilities.token_counting import TokenCounter
from app.core.magentrix_retriever import LangchainToLlammaRetriever
import asyncio
from llama_index.core.bridge.pydantic import BaseModel

async def summary_chat(history_store: str):
    loop = asyncio.get_running_loop()
    loaded_chat_store = SimpleChatStore.parse_raw(history_store)
    memory = ChatSummaryMemoryBuffer.from_defaults(
        chat_store=loaded_chat_store,    
        llm = Settings.llm,
        summarize_prompt="Please write a title for the latest conversation. Answer with 254 maximum characters and DO NOT place resonse within quotes, single quotes"
    )

    histories = loaded_chat_store.store['chat_history']
    histories = list(filter(lambda x: x.content and x.role != MessageRole.TOOL, histories))  
    summary = await loop.run_in_executor(None, memory._summarize_oldest_chat_history, histories)
    tokenizer = tiktoken.encoding_for_model("gpt-4o").encode
    token_counter = TokenCounter(tokenizer=tokenizer)
    input_llm_token = token_counter.get_string_tokens(memory.summarize_prompt + history_store)
    output_llm_token = token_counter.get_string_tokens(summary.content)

    return QueryResponse(summary.content, TokenUsageModel(input_llm_token + output_llm_token, 0, input_llm_token, output_llm_token, memory.llm.model))


"""Retriever tool."""

from typing import TYPE_CHECKING, Any, List, Optional

from llama_index.core.base.base_retriever import BaseRetriever

if TYPE_CHECKING:
    from llama_index.core.langchain_helpers.agents.tools import LlamaIndexTool
from llama_index.core.schema import MetadataMode, NodeWithScore, QueryBundle
from llama_index.core.tools.types import AsyncBaseTool, ToolMetadata, ToolOutput
from llama_index.core.postprocessor.types import BaseNodePostprocessor


class CustomRetrieverTool(AsyncBaseTool):
    """Retriever tool.

    A tool making use of a retriever.

    Args:
        retriever (BaseRetriever): A retriever.
        metadata (ToolMetadata): The associated metadata of the query engine.
        node_postprocessors (Optional[List[BaseNodePostprocessor]]): A list of
            node postprocessors.
    """

    def __init__(
        self,
        retriever: BaseRetriever,
        metadata: ToolMetadata,
        node_postprocessors: Optional[List[BaseNodePostprocessor]] = None,
    ) -> None:
        self._retriever = retriever
        self._metadata = metadata
        self._node_postprocessors = node_postprocessors or []

    @classmethod
    def from_defaults(
        cls,
        retriever: BaseRetriever,
        node_postprocessors: Optional[List[BaseNodePostprocessor]] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
    ) -> "CustomRetrieverTool":
        name = name or "retriever_tool"
        description = description or """Useful for running a natural language query
        against a knowledge base and retrieving a set of relevant documents.
        """

        metadata = ToolMetadata(name=name, description=description)
        return cls(
            retriever=retriever,
            metadata=metadata,
            node_postprocessors=node_postprocessors,
        )

    @property
    def retriever(self) -> BaseRetriever:
        return self._retriever

    @property
    def metadata(self) -> ToolMetadata:
        return self._metadata

    def call(self, *args: Any, **kwargs: Any) -> ToolOutput:
        query_str = ""

        if kwargs is not None:
            query_str = kwargs.get('input', "")
        if query_str == "":
            raise ValueError("Cannot call query engine without inputs")

        docs = self._retriever.retrieve(query_str)
        docs = self._apply_node_postprocessors(docs, QueryBundle(query_str))
        content = ""
        for doc in docs:
            node_copy = doc.node.copy()
            node_copy.text_template = "{metadata_str}\n{content}"
            node_copy.metadata_template = "{key} = {value}"
            content += node_copy.get_content(MetadataMode.LLM) + "\n\n"
        return ToolOutput(
            content=content,
            tool_name=self.metadata.name,
            raw_input={"input": input},
            raw_output=docs,
        )

    async def acall(self, *args: Any, **kwargs: Any) -> ToolOutput:
        query_str = ""

        if kwargs is not None:
            query_str = kwargs.get('input', "")
        if query_str == "":
            raise ValueError("Cannot call query engine without inputs")
        docs = await self._retriever.aretrieve(query_str)
        content = ""
        docs = self._apply_node_postprocessors(docs, QueryBundle(query_str))
        for doc in docs:
            node_copy = doc.node.copy()
            node_copy.text_template = "{metadata_str}\n{content}"
            node_copy.metadata_template = "{key} = {value}"
            content += node_copy.get_content(MetadataMode.LLM) + "\n\n"

        context = f"""
            "Relevant articles and wikis information from knowledge base:\n"
            "---------------------\n"
            {content}
            "---------------------\n"
        """
        return ToolOutput(
            content=context,
            tool_name=self.metadata.name,
            raw_input={"input": input},
            raw_output=docs,
        )

    def as_langchain_tool(self) -> "LlamaIndexTool":
        raise NotImplementedError("`as_langchain_tool` not implemented here.")

    def _apply_node_postprocessors(
        self, nodes: List[NodeWithScore], query_bundle: QueryBundle
    ) -> List[NodeWithScore]:
        for node_postprocessor in self._node_postprocessors:
            nodes = node_postprocessor.postprocess_nodes(
                nodes, query_bundle=query_bundle
            )
        return nodes



class CustomDetailRetrieverTool(AsyncBaseTool):
    """Detail Retriever tool.

    Args:
        retriever (BaseRetriever): A retriever.
        metadata (ToolMetadata): The associated metadata of the query engine.
        node_postprocessors (Optional[List[BaseNodePostprocessor]]): A list of
            node postprocessors.
    """

    def __init__(
        self,
        retriever: LangchainToLlammaRetriever,
        metadata: ToolMetadata,
        node_postprocessors: Optional[List[BaseNodePostprocessor]] = None,
    ) -> None:
        self._retriever = retriever
        self._metadata = metadata
        self._node_postprocessors = node_postprocessors or []

    @classmethod
    def from_defaults(
        cls,
        retriever: LangchainToLlammaRetriever,
        node_postprocessors: Optional[List[BaseNodePostprocessor]] = None,
        name: Optional[str] = None,
        description: Optional[str] = None,
    ) -> "CustomDetailRetrieverTool":
        name = name or "detail_retriever_tool"
        description = description or "useful for when you want to explore more details inside a Article or Wiki."

        metadata = ToolMetadata(name=name, description=description, fn_schema=DetailToolFnSchema)
        return cls(
            retriever=retriever,
            metadata=metadata,
            node_postprocessors=node_postprocessors,
        )

    @property
    def metadata(self) -> ToolMetadata:
        return self._metadata

    def call(self, *args: Any, **kwargs: Any) -> ToolOutput:
        pass

    async def acall(self, *args: Any, **kwargs: Any) -> ToolOutput:
        query_str = ""
        ids = []

        if kwargs is not None:
            ids = kwargs.get('ids', [])
            query_str = kwargs.get('search_query', "")
        
        if not ids:
            raise ValueError("Cannot call without ids")
        
        if query_str == "":
            raise ValueError("Cannot call without search_query")
        
        docs = await self._retriever.acall_retriever_function('aget_chunks_by_ids', ids, query_str)
        content = ""
        docs = self._apply_node_postprocessors(docs, QueryBundle(query_str))
        
        for doc in docs:
            node_copy = doc.node.copy()
            node_copy.text_template = "{metadata_str}\n{content}"
            node_copy.metadata_template = "{key} = {value}"
            content += node_copy.get_content(MetadataMode.LLM) + "\n\n"

        context = f"""
            "Relevant detail content of articles and wikis information from knowledge base:\n"
            "---------------------\n"
            {content}
            "---------------------\n"
        """
        return ToolOutput(
            content=context,
            tool_name=self.metadata.name,
            raw_input={"search_query": input},
            raw_output=docs,
        )

    def as_langchain_tool(self) -> "LlamaIndexTool":
        raise NotImplementedError("`as_langchain_tool` not implemented here.")

    def _apply_node_postprocessors(
        self, nodes: List[NodeWithScore], query_bundle: QueryBundle
    ) -> List[NodeWithScore]:
        for node_postprocessor in self._node_postprocessors:
            nodes = node_postprocessor.postprocess_nodes(
                nodes, query_bundle=query_bundle
            )

        return nodes
    
class DetailToolFnSchema(BaseModel):
    ids: list[str]
    search_query: str