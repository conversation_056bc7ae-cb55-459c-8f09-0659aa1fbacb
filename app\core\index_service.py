import logging

from app.core.config import app_settings, embedding_tokenizer, langchain_embeddings
import asyncio
from concurrent.futures import ThreadPoolExecutor
import nest_asyncio
nest_asyncio.apply()
from llama_index.core.utilities.token_counting import TokenCounter
from app.api.models.schema import TokenUsageModel
from langchain_milvus import Milvus, BM25BuiltInFunction
from langchain_mongodb.indexes import MongoDBRecordManager
from langchain_core.vectorstores import VectorStore
from langchain_core.indexing.base import RecordManager
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document
from functools import lru_cache
from app.core.magentrix_retriever import HybridRetriever, AdapterRetriever
import html2text
from app.api.models.schema import SourceDocument
from app.utils.util_functions import index_with_ids
from app.core.magentrix_client import MagentrixClient
from langchain_core.runnables import run_in_executor
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs.nebula_graph import NebulaGraph
from langchain_community.chains.graph_qa.nebulagraph import NebulaGraphQAChain
from langchain_community.chains.graph_qa.prompts import CYPHER_GENERATION_TEMPLATE
from llama_index.core import Settings
import os
import json
from datetime import datetime
import pymongo
from pymilvus import connections, utility, MilvusClient

logger = logging.getLogger(__name__)
executor = ThreadPoolExecutor(max_workers=10)

class LangchainIndexService():    
    def __init__(
        self,
        org_code: str,
    ) -> None:
        self._org_code: str = org_code
        self._magentrix_client: MagentrixClient = None

    def add_magentrix_client(self, magentrix_client: MagentrixClient):
        self._magentrix_client = magentrix_client

    def get_vector_store(self, collection_name: str) -> VectorStore:
        vectorstore = cached_vector_store(collection_name)

        return vectorstore
    
    def _get_record_manager(self, collection_name: str) -> RecordManager:
        namespace =  f"{self._org_code}.{collection_name}"
        record_manager = MongoDBRecordManager.from_connection_string(app_settings.MONGODB_URI, namespace)

        return record_manager

    async def abuild_indices(self, collection_name:str, documents: list[SourceDocument], cleanup="scoped_full"):
        lanchain_docs = []
        lanchain_docs = await self._get_langchain_documents(collection_name, documents)
        
        vector_store = self.get_vector_store(collection_name)
        record_manager = self._get_record_manager(collection_name)
        token_counter = TokenCounter(tokenizer=embedding_tokenizer)
        total_embedding_token = 0

        if len(lanchain_docs) == 0:
            if cleanup == "full":
                # Clean all data when no documents and cleanup is full
                await run_in_executor(None, index_with_ids, [], record_manager, vector_store, cleanup=cleanup)
            return TokenUsageModel(0, 0, 0, 0, langchain_embeddings.model)

        text_splitter = RecursiveCharacterTextSplitter(chunk_size=512, chunk_overlap=100)
        sub_docs = text_splitter.split_documents(lanchain_docs)
        idx = await run_in_executor(None, index_with_ids, sub_docs, record_manager, vector_store, cleanup=cleanup, source_id_key="Id")      

        if idx["num_added"] > 0:
            for doc_indexed in idx["docs_to_index"]:
                total_embedding_token += token_counter.get_string_tokens(doc_indexed.page_content)

        return TokenUsageModel(0, total_embedding_token, 0, 0, langchain_embeddings.model)

    async def _get_langchain_documents(self, collection_name: str, source_documents: list[SourceDocument]):
        langchain_docs = []

        if self._magentrix_client:
            # Run all document loading tasks concurrently
            document_tasks = [self._magentrix_client.aload_file(file, collection_name) for file in source_documents]
            documents = await asyncio.gather(*document_tasks)
            # Flatten the list of documents in case each task returns multiple documents
            for result in documents:
                if isinstance(result, Exception):
                    logger.error(f"Error loading document: {str(result)}")
                    continue
                if result is not None:
                    langchain_docs.extend(result)
        else:
            for document in source_documents:
                if (document.IndexType and document.Metadata):
                    document.Metadata['IndexType'] = document.IndexType
                
                if (document.Text):
                    if "Meeting Transcript" in document.Text and "Meeting Insight" in document.Text:
                        continue
                
                    entire_doc = Document(
                        id=document.Id,
                        page_content=html2text.html2text(document.Text),
                        metadata=document.Metadata
                    )

                    langchain_docs.append(entire_doc)

        detail_langchain_docs = []

        for entire_doc in langchain_docs:
            if (entire_doc.page_content):
                entire_doc.metadata['OrgCode'] = self._org_code  

                if entire_doc.metadata:
                    if "Name" in entire_doc.metadata and entire_doc.metadata["Name"] and "IgnoreIndexName" not in entire_doc.metadata:       
                        title_doc = Document(
                            page_content=entire_doc.metadata["Name"],
                            metadata=entire_doc.metadata
                        )

                        if "Description" in entire_doc.metadata:
                            del title_doc.metadata["Description"]

                        if "Summary" in entire_doc.metadata:
                            del title_doc.metadata["Summary"]

                        title_doc.metadata["IsTitle"] = True
                        detail_langchain_docs.append(title_doc)

                    entire_doc.metadata.pop("IsIndexName", None)

                    if "Description" in entire_doc.metadata and entire_doc.metadata["Description"]:         
                        description_doc = Document(
                            page_content=entire_doc.metadata["Description"],
                            metadata=entire_doc.metadata
                        )
                        del entire_doc.metadata["Description"]

                        description_doc.metadata["IsDescription"] = True
                        detail_langchain_docs.append(description_doc)

                    if "Summary" in entire_doc.metadata and entire_doc.metadata["Summary"]:
                        description_doc = Document(
                            page_content=entire_doc.metadata["Summary"],
                            metadata=entire_doc.metadata
                        )
                        del entire_doc.metadata["Summary"]

                        description_doc.metadata["IsDescription"] = True
                        detail_langchain_docs.append(description_doc)

                detail_langchain_docs.append(entire_doc)

        return detail_langchain_docs

    async def delete_document_by_ids(self, collection_name: str, delete_ids:list[str]):
        vector_store = self.get_vector_store(collection_name)
        record_manager = self._get_record_manager(collection_name)
        condition = f'OrgCode == \"{self._org_code}\" and Id in {json.dumps(delete_ids)}'

        await vector_store.adelete(expr=condition)
        deleted_keys = await record_manager.alist_keys(group_ids=delete_ids)
        await record_manager.adelete_keys(keys=deleted_keys)

        return True  
    
    async def delete_all_org_data(self):
        """
        Delete all MongoDB database and vector store data for this org.
        """
        # --- Delete MongoDB collections ---
        mongo_client = None
        try:
            mongo_client = pymongo.MongoClient(app_settings.MONGODB_URI)
            mongo_client.drop_database(self._org_code)
        finally:
            if mongo_client:
                mongo_client.close()

        # --- Delete Milvus collections ---
        vector_store_knowledge = self.get_vector_store("KnowledgeBase")
        vector_store_note = self.get_vector_store("NoteAndAttachment")
        vector_store_document = self.get_vector_store("Document")
        condition = f'OrgCode == \"{self._org_code}\"'

        await vector_store_knowledge.adelete(expr=condition)
        await vector_store_note.adelete(expr=condition)
        await vector_store_document.adelete(expr=condition)

        return True
    
    async def delete_all_org_database(self):
        """
        Delete all MongoDB databases (except default ones) and all Milvus collections.
        """
        # --- Delete MongoDB databases except default ones ---
        mongo_client = None
        try:
            mongo_client = pymongo.MongoClient(app_settings.MONGODB_URI)
            default_dbs = {"admin", "config", "local"}
            db_names = mongo_client.list_database_names()
            for db_name in db_names:
                if db_name not in default_dbs:
                    mongo_client.drop_database(db_name)
        finally:
            if mongo_client:
                mongo_client.close()

        # --- Delete all Milvus collections ---
        connections.connect(uri=app_settings.MILVUS_URI)
        all_collections = utility.list_collections()
        
        for collection_name in all_collections:
            utility.drop_collection(collection_name)
        
        connections.disconnect("default")

        return True

    def get_hybrid_retriever(self, name: str, 
                             cutoff: float = None, 
                             filters: str = None, 
                             focus_top : bool = False, 
                             weights : list[float] = None, 
                             top_k: int = 20,
                             search_mode: str = "hybrid"):
        if search_mode == "hybrid":
            vector_store = self.get_vector_store(collection_name=name)
        else:
            vector_store = get_semantic_vector_store(collection_name=name)

        retriever = HybridRetriever(org_code=self._org_code, vector_store=vector_store, k=top_k, thresh_hold=cutoff, filter_clause=filters, focus_top=focus_top, weights=weights, magentrix_client=self._magentrix_client)

        return retriever
    
    def get_adapter_retriever(self, name: str, top_k: int = 20, cutoff: float = 0.5, filters: str = None):
        vector_store = self.get_vector_store(collection_name=name)
        condition = f"OrgCode == \"{self._org_code}\""

        if filters:
            condition = f'{condition} and ({filters})'
        else:
            condition = f'{condition}'

        retriever = AdapterRetriever(org_code=self._org_code, vector_store=vector_store, k=top_k, thresh_hold=cutoff, condition=condition)

        return retriever

    def get_vector_graph_retriever(self, name: str):
        vector_store = self.get_vector_store(collection_name=name)

        from langchain_graph_retriever import GraphRetriever
        from langchain_graph_retriever.adapters.langchain import LangchainAdapter
        from graph_retriever.strategies import Eager

        traversal_retriever = GraphRetriever(
            store = LangchainAdapter(vector_store=vector_store),
            edges = [("Id", "Id")],
            strategy = Eager(k=5, start_k=1, max_depth=1),
        )

        return traversal_retriever

def get_semantic_vector_store(collection_name: str) -> VectorStore:
    # Check if collection exists and has indexes
    try:
        connections.connect(uri=app_settings.MILVUS_URI)
        collection_exists = utility.has_collection(collection_name)

        if collection_exists:
            # If collection exists, create without partition isolation to avoid conflicts
            vectorstore = Milvus(
                collection_name=collection_name,
                embedding_function=langchain_embeddings,
                connection_args={
                    "uri": app_settings.MILVUS_URI,
                },
                vector_field=["dense"],
                index_params=[{
                            "metric_type": "L2",
                            "index_type": "HNSW",
                            "params": {"M": 32, "efConstruction": 500},
                        }],
                search_params=[{"metric_type": "L2", "params": {"ef": 40}}],
                partition_key_field="OrgCode",
                enable_dynamic_field=True,
                num_shards=2
            )
        else:
            # If collection doesn't exist, create with partition isolation
            vectorstore = Milvus(
                collection_name=collection_name,
                embedding_function=langchain_embeddings,
                connection_args={
                    "uri": app_settings.MILVUS_URI,
                },
                vector_field=["dense"],
                index_params=[{
                            "metric_type": "L2",
                            "index_type": "HNSW",
                            "params": {"M": 32, "efConstruction": 500},
                        }],
                search_params=[{"metric_type": "L2", "params": {"ef": 40}}],
                partition_key_field="OrgCode",
                enable_dynamic_field=True,
                collection_properties={"partitionkey.isolation": True},
                num_shards=2
            )
    except Exception as e:
        logger.warning(f"Error checking collection existence: {e}. Creating without partition isolation.")
        # Fallback: create without partition isolation
        vectorstore = Milvus(
            collection_name=collection_name,
            embedding_function=langchain_embeddings,
            connection_args={
                "uri": app_settings.MILVUS_URI,
            },
            vector_field=["dense"],
            index_params=[{
                        "metric_type": "L2",
                        "index_type": "HNSW",
                        "params": {"M": 32, "efConstruction": 500},
                    }],
            search_params=[{"metric_type": "L2", "params": {"ef": 40}}],
            partition_key_field="OrgCode",
            enable_dynamic_field=True,
            num_shards=2
        )
    finally:
        try:
            connections.disconnect("default")
        except:
            pass

    return vectorstore

def cached_vector_store(collection_name: str) -> VectorStore:
    vectorstore = Milvus(
        collection_name=collection_name,
        embedding_function=langchain_embeddings,
        connection_args={
            "uri": app_settings.MILVUS_URI
        },
        builtin_function=BM25BuiltInFunction(),
        vector_field=["dense", "sparse"],
        index_params=[{
                        "metric_type": "L2",
                        "index_type": "HNSW",
                        "params": {"M": 32, "efConstruction": 500},
                    }, {
                        "metric_type": "BM25",
                        "index_type": "SPARSE_INVERTED_INDEX",
                        "params":{"inverted_index_algo": "DAAT_MAXSCORE"}
                    }],
        search_params=[{
                        "metric_type": "L2", 
                        "params": {"ef": 64 }
                    }, 
                    {
                        "metric_type": "BM25",
                        "params": {},
                    }],
        partition_key_field="OrgCode",
        enable_dynamic_field=True,
        collection_properties={"partitionkey.isolation": True},
        num_shards=2
    )

    return vectorstore