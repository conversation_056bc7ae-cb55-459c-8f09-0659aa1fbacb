# AI Assistant Setup Guide

## Prerequisites

### 1. Install Docker Engine on Linux
Follow the official Docker installation guide:  
👉 [Docker Engine for Ubuntu](https://docs.docker.com/engine/install/ubuntu/)

### 2. Install Python
Download and install the latest version of Python:  
👉 [Python Downloads](https://www.python.org/downloads/)

### 3. Install Milvus using Docker
Follow the steps outlined in the Milvus documentation:  
👉 [Milvus Standalone Installation (Docker)](https://milvus.io/docs/install_standalone-docker.md)

### 4. Install MongoDB using Docker
Run the following commands:
```bash
sudo docker pull mongodb/mongodb-community-server:latest
sudo docker run --name mongodb --restart=always -p 27017:27017 -d mongodb/mongodb-community-server:latest
```

# Start the assistant at root folder
### Development 
Open Visual Code -> F5 to debug

### Docker production
Execute the following command inside the AIAssistant folder created above:
```bash
sudo docker-compose up --build -d
```
