[tool.poetry]
name = "magentrix-rag-engine"
version = "0.1.0"
description = "This is internal RESTFULL API to support AI"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.110.0"
uvicorn = "^0.29.0"
llama-index = ">=0.10.22,<0.11.0"
pydantic-settings = "^2.2.1"
aiohttp = "^3.9.5"
motor = "^3.5.0"
openpyxl = "^3.1.5"
html2text = "^2024.2.26"
python-pptx = "^1.0.1"
chromadb = "^0.5.4"
openai = "^1.42.0"
youtube-transcript-api = "^0.6.2"
langchain = "^0.3.18"
langchain-milvus = "^0.1.8"
langchain-openai = "^0.3.4"
langchain-mongodb = "^0.4.0"
langchain-community = "^0.3.17"
langchain-experimental = "^0.3.4"
langgraph = "^0.3.1"
langsmith = "^0.3.11"
pydub = "^0.25.1"
speechrecognition = "^3.14.1"
openai-whisper = "^20240930"
soundfile = "^0.13.1"
langchain-graph-retriever = "^0.6.1"
nebula3-python = "^3.8.3"
python-docx = "^1.1.2"
langchain-google-genai = "^2.1.4"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
