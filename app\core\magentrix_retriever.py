from typing import List

from langchain_core.callbacks import (
    AsyncCallbackManagerForRetrieverRun,
    CallbackManagerForRetrieverRun,
)
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from langchain_core.vectorstores import VectorStore
from llama_index.core import QueryBundle
from llama_index.core.schema import NodeWithScore, TextNode
from llama_index.core.retrievers import BaseRetriever as LlamaBaseRetriever
from langchain_core.runnables import run_in_executor
from langchain.retrievers.document_compressors import LLMChainFilter
from app.core.config import langchain_llm, langchain_embeddings
from langchain.retrievers.document_compressors import EmbeddingsFilter
from app.core.magentrix_client import MagentrixClient
from langchain_core.prompts import (
    BasePromptTemplate,
    PromptTemplate,
    aformat_document,
    format_document,
)

class HybridRetriever(BaseRetriever):
    """A retriever that fetches the top-k documents containing the user query."""
    def __init__(
        self, 
        org_code: str, 
        vector_store: VectorStore, 
        k: int = 20,
        thresh_hold: float = None, 
        focus_top: bool = False, 
        filter_clause: str = None, 
        weights: list[float] = None,
        magentrix_client: MagentrixClient = None
    ) -> None:
        """Init params."""
        super().__init__()

        if not org_code:
            raise ValueError("Org Code is required")
        
        self._org_code = org_code
        self._top_k = k
        self._vector_store = vector_store
        self._thresh_hold = thresh_hold
        self._focus_top = focus_top
        self._filter_clause = filter_clause
        self._base_condition = f'OrgCode == "{org_code}"'
        self._weights = weights or [0.6, 0.4]
        self._magentrix_client = magentrix_client

    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        """Synchronous method to retrieve relevant documents."""
        documents = []
        condition = self._get_filters_statement(self._filter_clause)
        docs_and_similarities = self._vector_store.similarity_search_with_score(
            query, k=self._top_k, ranker_type="weighted", ranker_params={"weights": self._weights}, expr=condition
        )

        for doc, similarity in docs_and_similarities:
            doc.metadata["score"] = similarity
            if not self._thresh_hold:
                documents.append(doc)
            elif similarity >= self._thresh_hold:
                documents.append(doc)

        # Check permissions before returning
        if self._magentrix_client:
            documents = self._check_permissions(documents)

        return documents

    def _check_permissions(self, docs: List[Document]) -> List[Document]:
        """Check permissions for documents."""
        if not self._magentrix_client:
            return docs

        articleIds = []
        wikiIds = []
        attachmentIds = []
        documentIds = []

        for doc in docs:
            if doc.metadata["IndexType"] in ["CaseArticle", "Article"]:
                articleIds.append(doc.metadata["Id"])
            elif doc.metadata["IndexType"] in ["CaseWiki", "Wiki"]:
                wikiIds.append(doc.metadata["Id"])
            elif doc.metadata["IndexType"] in ["NoteAndAttachment"]:
                attachmentIds.append(doc.metadata["Id"])
            elif doc.metadata["IndexType"] in ["Document"]:
                documentIds.append(doc.metadata["Id"])

        permission = self._magentrix_client.fetchPermission(
            list(set(articleIds)), 
            list(set(wikiIds)),
            list(set(attachmentIds)),
            list(set(documentIds))
        )

        accessible_ids = permission.get('GeneralIds', [])
        attachment_accessible_ids = permission.get('NoteAndAttachmentIds', [])
        document_accessible_ids = permission.get('DocumentIds', [])

        if not accessible_ids and not attachment_accessible_ids and not document_accessible_ids:
            return []

        return [
            doc for doc in docs 
            if doc.metadata.get("Id") in accessible_ids 
            or doc.metadata.get("Id") in attachment_accessible_ids
            or doc.metadata.get("Id") in document_accessible_ids
        ]

    async def _aget_relevant_documents(
        self, 
        query: str,
        *, 
        run_manager: AsyncCallbackManagerForRetrieverRun
    ) -> List[Document]:
        """Get documents after checking permissions."""
        documents = []
        condition = self._get_filters_statement(self._filter_clause)
        docs_and_similarities = await self._vector_store.asimilarity_search_with_score(
            query, 
            k=self._top_k,
            ranker_type="weighted",
            ranker_params={"weights": self._weights},
            expr=condition
        )

        for doc, similarity in docs_and_similarities:
            doc.metadata["score"] = similarity
            if not self._thresh_hold or similarity >= self._thresh_hold:
                documents.append(doc)

        if self._focus_top:
            documents = await run_in_executor(None, self._get_top_article_chunks, documents)
        else:
            documents = [doc for doc in documents if "IsDescription" not in doc.metadata]

        # Check permissions before returning
        if self._magentrix_client:
            documents = await run_in_executor(None, self._check_permissions, documents)

        return documents
    
    def _get_top_article_chunks(
        self, docs_and_similarities: List[Document]
    ) -> List[Document]:
        """Process retrieved documents synchronously."""
        if not docs_and_similarities:
            return []

        langchain_docs = []
        processed_ids = set()

        for doc in docs_and_similarities: 
            doc_id = doc.metadata["Id"]

            if doc.metadata["score"] > 0.8 and doc_id not in processed_ids:        
                """Retrieve relevant chunks of the top-scoring document."""          
                condition = self._get_filters_statement(f'Id == "{doc_id}"')
                chunks = self._vector_store.col.query(
                    expr=condition,
                    output_fields=["text", "IsTitle", "IsDescription"],
                )
                parsed_chunks = [chunk for chunk in chunks]

                top_chunks = [Document(page_content=chunk["text"], metadata=doc.metadata)
                                for chunk in parsed_chunks if "IsDescription" not in chunk]
                
                processed_ids.add(doc_id)
                langchain_docs.extend(top_chunks)
        
        if len(langchain_docs) == 0:
            langchain_docs = docs_and_similarities
        
        return langchain_docs

    def get_chunks_by_ids(self, ids: list[str], query: str) -> tuple[str, list[Document]]:
        """Get chunks by IDs with permission checking."""
        base_condition = self._get_filters_statement(self._filter_clause)
        id_expr = f"Id in [{', '.join(f'\"{doc_id}\"' for doc_id in ids)}]"
        condition = f'{base_condition} and {id_expr} and IsTitle != true and IsDescription != true'
        docs_and_similarities = self._vector_store.similarity_search_with_score(
            query, k=self._top_k, ranker_type="weighted", ranker_params={"weights": [0.8, 0.2]}, expr=condition
        )
        documents = []
        
        for doc, similarity in docs_and_similarities:
            doc.metadata["score"] = similarity
            documents.append(doc)

        # Check permissions if Magentrix client is available
        if self._magentrix_client:
            documents = self._check_permissions(documents)
            if not documents:
                return ("No accessible documents found.", [])

        doc_prompt = PromptTemplate.from_template(
            "<doc>\n{page_content}\n<meta>\nId: {Id}\n IndexType: {IndexType}\n</meta>\n</doc>"
        )

        content = "\n---\n".join(
                [format_document(doc, doc_prompt) for doc in documents]
            )

        return (content, documents)
    
    async def aget_chunks_by_ids(self, ids: list[str], query: str) -> tuple[str, list[Document]]:
        """Async version of get_chunks_by_ids with permission checking."""
        base_condition = self._get_filters_statement(self._filter_clause)
        id_expr = f"Id in [{', '.join(f'\"{doc_id}\"' for doc_id in ids)}]"
        condition = f'{base_condition} and {id_expr} and IsTitle != true and IsDescription != true'
        
        docs_and_similarities = await self._vector_store.asimilarity_search_with_score(
            query, 
            k=self._top_k, 
            ranker_type="weighted", 
            ranker_params={"weights": [0.8, 0.2]}, 
            expr=condition
        )
        
        documents = []
        for doc, similarity in docs_and_similarities:
            doc.metadata["score"] = similarity
            documents.append(doc)

        # Check permissions if Magentrix client is available
        if self._magentrix_client:
            documents = await run_in_executor(None, self._check_permissions, documents)
            if not documents:
                return ("No accessible documents found.", [])

        doc_prompt = PromptTemplate.from_template(
            "<doc>\n{page_content}\n<meta>\nId: {Id}\n IndexType: {IndexType}\n</meta>\n</doc>"
        )

        content = "\n---\n".join(
                [await aformat_document(doc, doc_prompt) for doc in documents]
            )

        return (content, documents)
    

    def aquery(self, condition: str, output_fields):
        chunks = self._vector_store.col.query(
            expr=condition,
            output_fields=output_fields,
        )

        return chunks

    def _get_filters_statement(self, filter_clause: str) -> str:
        return f'{self._base_condition} and ({filter_clause})' if filter_clause else f'{self._base_condition}'
        
class AdapterRetriever(LlamaBaseRetriever):
    """Custom retriever that transfer Milvus vector search to llama nodes."""
    def __init__(
        self,
        org_code: str,
        vector_store: VectorStore,
        condition: str,
        k: int = 8,
        thresh_hold: float = 0.5,
    ) -> None:
        super().__init__()
        """Init params."""
        self._vector_store = vector_store
        self._thresh_hold = thresh_hold
        self._top_k = k
        self._condition = condition
        self._org_code = org_code

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes given query."""
        docs_and_similarities = self._vector_store.similarity_search_with_score(
            query_bundle.query_str,
            k=self._top_k,
            ranker_type="weighted",
            ranker_params={"weights": [0.6, 0.4]},
            expr=self._condition,
        )
        nodes_with_scores = self._parse_documents_from_search_results(query_bundle.query_str, docs_and_similarities)
        
        return nodes_with_scores
    
    async def _aretrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes given query."""
        docs_and_similarities = await self._vector_store.asimilarity_search_with_score(
            query_bundle.query_str,
            k=self._top_k,
            ranker_type="weighted",
            ranker_params={"weights": [0.6, 0.4]},
            expr=self._condition,
        )
        nodes_with_scores = await self._aparse_documents_from_search_results_async(query_bundle.query_str, docs_and_similarities)
        
        return nodes_with_scores
    
    def _parse_documents_from_search_results(
        self, query: str, docs_and_similarities: List[tuple[Document, float]]
    ) -> List[NodeWithScore]:
        """Process retrieved documents synchronously."""
        if not docs_and_similarities:
            return []

        langchain_docs = []
        filter = LLMChainFilter.from_llm(langchain_llm)

        if docs_and_similarities[0][1] > 0.75:
            langchain_docs.extend(self._get_top_article_chunks(docs_and_similarities[0]))
        else:
            for doc, score in docs_and_similarities:
                if score > self._thresh_hold:
                    doc.metadata["score"] = score
                    langchain_docs.append(doc)
        
        filter_docs = filter.compress_documents(documents=langchain_docs, query=query)
        
        return self._convert_docs_to_nodes(filter_docs)

    async def _aparse_documents_from_search_results_async(
        self, query: str, docs_and_similarities: List[tuple[Document, float]]
    ) -> List[NodeWithScore]:
        """Process retrieved documents asynchronously."""
        if not docs_and_similarities:
            return []

        langchain_docs = []
        filter = LLMChainFilter.from_llm(langchain_llm)

        if docs_and_similarities[0][1] > 0.75:
            top_chunks = await run_in_executor(None, self._get_top_article_chunks, docs_and_similarities[0])
            langchain_docs.extend(top_chunks)
        else:
            for doc, score in docs_and_similarities:
                if score > self._thresh_hold:
                    doc.metadata["score"] = score
                    langchain_docs.append(doc)
            
        filter_docs = await filter.acompress_documents(documents=langchain_docs, query=query)

        return self._convert_docs_to_nodes(filter_docs)

    def _get_top_article_chunks(self, top_doc: tuple[Document, float]) -> List[Document]:
        """Retrieve relevant chunks of the top-scoring document."""
        doc_id = top_doc[0].metadata["Id"]
        chunks = self._vector_store.col.query(
            expr=f'OrgCode == "{self._org_code}" and Id == "{doc_id}"',
            output_fields=["text", "IsTitle", "IsDescription"],
        )

        return [
            Document(page_content=chunk["text"], metadata={**top_doc[0].metadata, "score": top_doc[1]})
            for chunk in chunks if "IsTitle" not in chunk and "IsDescription" not in chunk
        ]

    def _convert_docs_to_nodes(self, docs: List[Document]) -> List[NodeWithScore]:
        """Convert documents to NodeWithScore objects."""
        excluded_keys = [
            "Id", "Name", "OrgCode", "pk", "Status", "Tags", "WikiId", "BlogId", "Description", "Summary", "IsTitle", "IsDescription"
        ]
        nodes_with_scores = []

        for doc in docs:
            score = doc.metadata.pop("score", None)
            node = TextNode(text=doc.page_content, metadata=doc.metadata, excluded_llm_metadata_keys=excluded_keys)
            nodes_with_scores.append(NodeWithScore(node=node, score=score))

        return nodes_with_scores

class LangchainToLlammaRetriever(LlamaBaseRetriever):
    """Custom retriever that transfer Milvus vector search to llama nodes."""
    def __init__(
        self,
        retriever: BaseRetriever
    ) -> None:
        super().__init__()
        """Init params."""
        self._retriever = retriever

    def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes given query."""
        langchain_docs = self._retriever.invoke(query_bundle.query_str)
        nodes_with_scores = self._convert_docs_to_nodes(langchain_docs)

        return nodes_with_scores
    
    async def _aretrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
        """Retrieve nodes given query."""
        langchain_docs = await self._retriever.ainvoke(query_bundle.query_str)

        for doc in langchain_docs:
            title = doc.metadata.get("Name", "")
            body = doc.page_content
            doc.page_content = f"Title: {title} \n Content: {body}" if title else body

        if langchain_docs:
            embeddings_filter = EmbeddingsFilter(embeddings=langchain_embeddings, similarity_threshold=0.3)  
            filter_docs = await embeddings_filter.acompress_documents(documents=langchain_docs, query=query_bundle.query_str)
        else:
            filter_docs = []
        
        nodes_with_scores = self._convert_docs_to_nodes(filter_docs)
        
        return nodes_with_scores
    
    async def acall_retriever_function(self, function_name: str, *args, **kwargs):
        """Call any method of the retriever instance."""
        # Ensure the retriever has the method
        if not hasattr(self._retriever, function_name):
            raise AttributeError(f"'{self._retriever.__class__.__name__}' object has no method '{function_name}'")
        
        # Get the method from retriever and call it with arguments
        method = getattr(self._retriever, function_name)
        docs = await method(*args, **kwargs)
        nodes_with_scores = self._convert_docs_to_nodes(docs)
        
        return nodes_with_scores

    def _convert_docs_to_nodes(self, docs: List[Document]) -> List[NodeWithScore]:
        """Convert documents to NodeWithScore objects."""
        excluded_keys = [
           "Name", "OrgCode", "pk", "Status", "Tags", "WikiId", "BlogId", "Description", "Summary", "IsTitle", "IsDescription", "Reference.WikiId", "Reference.BlogId", "ReferenceId", 
        ]
        nodes_with_scores = []

        for doc in docs:
            score = doc.metadata.pop("score", None)
            node = TextNode(text=doc.page_content, metadata=doc.metadata, excluded_llm_metadata_keys=excluded_keys)
            nodes_with_scores.append(NodeWithScore(node=node, score=score))

        return nodes_with_scores