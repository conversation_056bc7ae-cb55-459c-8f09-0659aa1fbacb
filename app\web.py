from app.core.config import app_settings
import os
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from app.api.router import api_router
import time
import logging
import sys

app = FastAPI(title='Magentrix AI Assistant', description='Generative AI Assistant to search, query, chat anything about knowledge base data with LLM', swagger_ui_parameters={"displayRequestDuration": True})
app.include_router(api_router)
logger = logging.getLogger()
logging.basicConfig(stream=sys.stdout, level=logging.DEBUG)
#logging.getLogger().addHandler(logging.StreamHandler(stream=sys.stdout))

common_directory = f"{app_settings.BASE_FOLDER_PATH}/storage"
common_log_path = f"{common_directory}/common_logs.log"

if not os.path.exists(common_directory):
    os.makedirs(common_directory)

if not os.path.exists(common_log_path): 
    with open(common_log_path, 'w') as file: 
        file.write("Log created!!!")

global_file_handler = logging.FileHandler(common_log_path)
global_formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
global_file_handler.setFormatter(global_formatter)
global_file_handler.setLevel(logging.INFO)
logger.addHandler(global_file_handler)

@app.exception_handler(Exception)
async def validation_exception_handler(request: Request, exc: Exception):
    directory = f"{app_settings.BASE_FOLDER_PATH}/storage"
    log_path = f"{directory}/logs.log"

    if "org_code" in request.path_params:
        directory = f"{app_settings.BASE_FOLDER_PATH}/storage/{request.path_params['org_code']}"
        log_path = f"{directory}/logs.log"

    if not os.path.exists(directory):
        os.makedirs(directory)

    if not os.path.exists(log_path): 
        with open(log_path, 'w') as file: 
            file.write("Log created!!!") 
        
    file_handler = logging.FileHandler(log_path)
    # Create and set formatter with timestamp
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_handler.setFormatter(formatter)

    logger = logging.getLogger("client_logger")
    logger.handlers.clear()
    logger.addHandler(file_handler)     
    logger.critical(exc, exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "message": (
                f"Failed method {request.method} at URL {request.url}."
                f" Exception message is {exc!r}."
            )
        },
    )
