
from llama_index.core import QueryBundle
from llama_index.core.postprocessor.types import BaseNodePostprocessor
from llama_index.core.schema import NodeWithScore
from typing import List, Optional
from app.core.magentrix_client import MagentrixClient
import asyncio
import nest_asyncio
nest_asyncio.apply()

class CasePostprocessor(BaseNodePostprocessor):
    """Filter-based Node processor."""

    @classmethod
    def class_name(cls) -> str:
        return "CasePostprocessor"

    def _postprocess_nodes(
        self,
        nodes: List[NodeWithScore],
        query_bundle: Optional[QueryBundle] = None,
    ) -> List[NodeWithScore]:
        """Postprocess nodes."""

        new_nodes = []

        for node_with_score in nodes:
            if node_with_score.node.metadata["IndexType"] in ["CaseArticle", "CaseWiki"]:
                new_nodes.append(node_with_score)                

        return new_nodes

class DocumentPostprocessor(BaseNodePostprocessor):
    """Filter-based Node processor."""

    @classmethod
    def class_name(cls) -> str:
        return "DocumentPostprocessor"

    def _postprocess_nodes(
        self,
        nodes: List[NodeWithScore],
        query_bundle: Optional[QueryBundle] = None,
    ) -> List[NodeWithScore]:
        """Postprocess nodes."""

        new_nodes = []

        for node_with_score in nodes:
            if node_with_score.node.metadata["IndexType"] in ["Document"]:
                new_nodes.append(node_with_score)                

        return new_nodes

class PermissionPostprocessor(BaseNodePostprocessor):
    """Permission-based Node processor."""

    magentrix_client: MagentrixClient

    @classmethod
    def class_name(cls) -> str:
        return "PermissionPostprocessor"

    def _postprocess_nodes(
        self,
        nodes: List[NodeWithScore],
        query_bundle: Optional[QueryBundle] = None,
    ) -> List[NodeWithScore]:
        """Postprocess nodes."""

        articleIds = []
        wikiIds = []
        attachmentIds = []

        for noda_data in nodes:
            if noda_data.node.metadata["IndexType"] in ["CaseArticle", "Article"]:
                articleIds.append(noda_data.node.metadata["Id"])

            if noda_data.node.metadata["IndexType"] in ["CaseWiki", "Wiki"]:
                wikiIds.append(noda_data.node.metadata["Id"])

            if noda_data.node.metadata["IndexType"] in ["NoteAndAttachment"]:
                attachmentIds.append(noda_data.node.metadata["Id"])

        permission = self.magentrix_client.fetchPermission(list(set(articleIds)), list(set(wikiIds)), list(set(attachmentIds)))
        accessible_ids = permission['GeneralIds']
        attachment_accessible_ids = permission['NoteAndAttachmentIds']

        if not accessible_ids and not attachment_accessible_ids:
            return []
        
        new_nodes = [node for node in nodes if node.node.metadata.get("Id") in accessible_ids or node.node.metadata.get("Id") in attachment_accessible_ids]    

        return new_nodes

class PublishedDocumentPostprocessor(BaseNodePostprocessor):
    """Filter-based Node processor."""

    @classmethod
    def class_name(cls) -> str:
        return "PublishedDocumentPostprocessor"

    def _postprocess_nodes(
        self,
        nodes: List[NodeWithScore],
        query_bundle: Optional[QueryBundle] = None,
    ) -> List[NodeWithScore]:
        """Postprocess nodes."""

        new_nodes = []

        for node_with_score in nodes:
            if "Status" in node_with_score.node.metadata and node_with_score.node.metadata["Status"] in ["Published"]:
                new_nodes.append(node_with_score)                

        return new_nodes