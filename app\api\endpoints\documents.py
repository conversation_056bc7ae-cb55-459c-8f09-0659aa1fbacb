import logging
from app.api.models.schema import SourceDocument, Chat<PERSON><PERSON><PERSON>
from fastapi import APIRouter, Head<PERSON>
from typing import List, Annotated
from app.core.index_service import LangchainIndexService
from app.core.agent import Agent
from app.core.tools import summary_chat
from fastapi import Body
from app.core.magentrix_client import MagentrixClient
from langsmith import Client, tracing_context
import asyncio

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/{org_code}/documents/{collection_name}")
async def upsert_documents(org_code: str, collection_name: str, documents: List[SourceDocument]):          
    service = LangchainIndexService(org_code)
    token_usage = await service.abuild_indices(collection_name, documents, "scoped_full")

    return token_usage

@router.post("/{org_code}/documents/{collection_name}/fullindex")
async def reindex_documents(org_code: str, collection_name: str, documents: List[SourceDocument]):          
    service = LangchainIndexService(org_code)
    token_usage = await service.abuild_indices(collection_name, documents, "full")

    return token_usage

@router.delete("/{org_code}/documents/{collection_name}")
async def delete_documents(org_code: str, collection_name: str, doc_ids: List[str] = None):
    service = LangchainIndexService(org_code)
    is_ok = await service.delete_document_by_ids(collection_name, doc_ids)

    return is_ok

@router.post("/{org_code}/query")
async def query(org_code: str, q: str, filters: dict=None, user_token: Annotated[str | None, Header()] = {}, callback_url: Annotated[str | None, Header()] = None):
    magentrix_client = MagentrixClient(user_token, callback_url)
    agent = Agent(org_code, magentrix_client)
    
    return await agent.aquery(q, filters)

@router.post("/{org_code}/retrieve")
async def retrieve(org_code: str, q: str, cutoff: float = None, filters: Annotated[dict | None, Body()] = {}, user_token: Annotated[str | None, Header()] = None, callback_url: Annotated[str | None, Header()] = None):
    magentrix_client = MagentrixClient(user_token, callback_url)
    agent = Agent(org_code, None)
    
    return await agent.aretrieve(q, filters, cutoff)

@router.get("/{org_code}/suggest")
async def suggest(org_code: str, q: str | None = None, size: int = 10, user_token: Annotated[str | None, Header()] = None, callback_url: Annotated[str | None, Header()] = None):
    magentrix_client = MagentrixClient(user_token, callback_url)
    agent = Agent(org_code, magentrix_client)
    
    return await agent.asuggest_document(q)

@router.post("/{org_code}/chat")
async def chat(org_code: str, request: ChatParams, user_token: Annotated[str | None, Header()] = None, callback_url: Annotated[str | None, Header()] = None):
    magentrix_client = MagentrixClient(user_token, callback_url)
    agent = Agent(org_code, magentrix_client)
    agent.add_chat_history(request.History)

    return await agent.achat_native_function_call(request.Query, request.Filters)
    
@router.post("/{org_code}/summarychat")
async def summarychat(org_code: str, history_store: str = Body(...)):
    response = await summary_chat(history_store)

    return response

@router.post("/{org_code}/filedocuments/{collection_name}")
async def file_documents(org_code: str, files: List[SourceDocument],  collection_name: str = "document", user_token: Annotated[str | None, Header()] = None, callback_url: Annotated[str | None, Header()] = None):
    magentrix_client = MagentrixClient(user_token, callback_url)
    service = LangchainIndexService(org_code)
    service.add_magentrix_client(magentrix_client)

    await service.abuild_indices(collection_name, files)

    return {"message": "OK"}

@router.delete("/{org_code}/alldata")
async def delete_all_org_data(org_code: str):
    """
    Delete all MongoDB collections and vector store data for the given organization.
    """
    service = LangchainIndexService(org_code)
    result = await service.delete_all_org_data()

    return {"success": result}

@router.delete("/{org_code}/alldatabase")
async def delete_all_org_database(org_code: str):
    """
    Delete all MongoDB collections and vector store. Internal usage only
    """
    if org_code != "0000":
        return
    
    service = LangchainIndexService(org_code)
    result = await service.delete_all_org_database()

    return {"success": result}