FROM python:3.12

RUN apt-get -y update
RUN apt-get -y upgrade
RUN apt-get install -y ca-certificates
RUN apt-get install -y ffmpeg

ENV PATH="/usr/local/bin:${PATH}"

ENV PYTHONUNBUFFERED=1

ENV POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_CREATE=0 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

WORKDIR /app

RUN ffmpeg -version
COPY pyproject.toml poetry.lock README.md ./
RUN pip install poetry==1.8.5
RUN poetry lock --no-update || poetry lock

RUN poetry install --no-root --no-dev

COPY ./app /app/app

# Command to run FastAPI using Uvicorn
CMD ["poetry", "run", "python", "-m", "app.main"]
